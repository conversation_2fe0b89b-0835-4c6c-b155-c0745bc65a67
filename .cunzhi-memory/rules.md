# 开发规范和规则

- 修复了IMCallbackHandler中导致长按工具弹窗失效的问题：外层和内层消息对象的sequence和time字段必须保持一致，外层time使用秒级时间戳，sequence使用实际值而非硬编码0
- 修复了UploadFile组件中的上传状态判断逻辑问题：当文件大小验证失败时，将return改为throw Error，添加了successCount、failedCount、failedReasons计数器来准确统计上传结果，根据成功/失败情况显示不同的提示信息，确保只有真正成功时才显示成功提示
- 调整了UploadFile组件的分片上传策略：1)统一文件大小限制为20MB（图片和视频）；2)分片上传触发阈值调整为5MB；3)动态分片大小配置：5-10MB文件使用1MB分片，10-20MB文件使用2MB分片；4)并发数保持3个；5)确保进度回调正常工作
- 删除历史记录消息API的请求参数格式已修改：从对象格式{integers: [267484]}改为直接传递数组[267484]。对应修改了DeleteHistoryMessagesParams类型定义为number[]类型，deleteHistoryMessages函数直接接收和发送数组数据。
