<template>
  <div class="chat">
    <div :class="['tui-chat', !isPC && 'tui-chat-h5']">
      <div v-if="!currentConversationID" :class="['tui-chat-default', !isPC && 'tui-chat-h5-default']">
        <slot />
      </div>
      <div v-if="currentConversationID" :class="['tui-chat', !isPC && 'tui-chat-h5']">
        <ChatHeader ref="chatHeaderRef"
          :class="['tui-chat-header', !isPC && 'tui-chat-H5-header', isUniFrameWork && 'tui-chat-uniapp-header']"
          :isGroup="isGroup" :headerExtensionList="headerExtensionList" @closeChat="closeChat"
          @openGroupManagement="handleGroup" :isMultipleSelectMode="isMultipleSelectMode"
          @toggleMultipleSelectMode="toggleMultipleSelectMode" @openHistoryChat="handleOpenHistoryChat"
          @exitHistoryMode="handleExitHistoryMode" />
        <Forward @toggleMultipleSelectMode="toggleMultipleSelectMode" />
        <MessageList ref="messageListRef" :class="['tui-chat-message-list', !isPC && 'tui-chat-h5-message-list']"
          :isGroup="isGroup" :groupID="groupID" :isNotInGroup="isNotInGroup"
          :isMultipleSelectMode="isMultipleSelectMode" :toAccount="toAccount" :userId="userId"
          @handleEditor="handleEditor" @closeInputToolBar="() => changeToolbarDisplayType('none')"
          @toggleMultipleSelectMode="toggleMultipleSelectMode" />
        <div v-if="isNotInGroup" :class="{
          'tui-chat-leave-group': true,
          'tui-chat-leave-group-mobile': isMobile,
        }">
          {{ leaveGroupReasonText }}
        </div>
        <MultipleSelectPanel v-if="isMultipleSelectMode" @deleteMessage="deleteMessage" @saveMessage="saveMessage"
          @quoteMessage="quoteMessage" class="multiple-select-overlay" />
        <!-- <MultipleSelectPanel v-else-if="isMultipleSelectMode" @onDelete="onDelete" @onSave="onSave" @onQuote="onQuote" /> -->
        <div class="ai-bottom" :class="{ 'ai-bottom-hidden': isMultipleSelectMode }">
          <div class="cite" v-if="chatStore.quoteList.length">
            <div v-for="item in chatStore.quoteList" :key="item.messageID" class="cite-item">
              <div class="cite-text" v-if="item.type === 'text'">{{ item.userName }}:&ensp;{{ item.data }}</div>
              <div class="cite-image" v-else-if="item.type === 'image'">
                <div class="cite-title">{{ item.userName }}:</div>
                <div class="img">
                  <img :src="item.data" />
                </div>
              </div>
              <div class="cite-video" v-else-if="item.type === 'video'">
                <div class="cite-title">{{ item.userName }}:</div>
                <div class="img">
                  <img :src="item.cover" />
                  <img class="play" :src="videoPlayImg" alt="" />
                </div>
              </div>
              <div v-else-if="item.type === 'audio'" class="cite-audio">
                <div class="cite-title">{{ item.userName }}:</div>
                <div class="img">
                  <img :src="audioChatImg" />
                </div>
              </div>
              <!-- <div v-else-if="item.type === 'other'">其他</div> -->
              <div class="remove" @click="chatStore.removeQuoteList(item)">
                <img :src="removeIcon" />
              </div>
            </div>
          </div>
          <div class="ai-toolbar" v-if="!chatStore?.specialCourse">
            <div class="ai-tool-list">
              <div class="ai-tool-item" v-for="item in chatStore.fourTools" @click="handleTool(item)"
                :key="item.functionName">
                <div class="ai-tool-item-icon">
                  <img :src="item.icon" />
                </div>
                <div class="ai-tool-item-text">{{ item.functionName?.replace('/', '') }}</div>
              </div>
            </div>

            <div class="ai-more" @click="handleAiMore">
              <div class="ai-more-icon"></div>
              <div class="ai-more-text">更多</div>
            </div>
          </div>
          <MessageInput ref="messageInputRef"
            :class="['tui-chat-message-input', !isPC && 'tui-chat-h5-message-input', isUniFrameWork && 'tui-chat-uni-message-input', isWeChat && 'tui-chat-wx-message-input']"
            :enableAt="featureConfig.InputMention" :isMuted="false"
            :muteText="TUITranslateService.t('TUIChat.您已被管理员禁言')" :placeholder="TUITranslateService.t('TUIChat.请输入消息')"
            :inputToolbarDisplayType="inputToolbarDisplayType" @changeToolbarDisplayType="changeToolbarDisplayType" />
        </div>
        <div :style="'height:' + (chatStore.quoteList.length ? '250px' : '163px')"></div>
      </div>
      <!-- Group Management -->
      <div v-if="!isNotInGroup && !isApp && isUniFrameWork && isGroup && headerExtensionList.length > 0"
        class="group-profile" @click="handleGroup">
        {{ headerExtensionList[0].text }}
      </div>
    </div>
    <SlideUpModal v-model:visible="chatStore.selectTextShow" @close="handleClose"> </SlideUpModal>
    <!-- 历史对话弹窗 -->
    <ChatHistoryModal :visible="showHistoryModal" @close="handleCloseHistoryModal" @select="handleSelectHistoryItem" />
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed } from '../../adapter-vue'
import TUIChatEngine, { TUITranslateService, TUIConversationService, TUIStore, StoreName, IMessageModel, IConversationModel, TUIChatService } from '@tencentcloud/chat-uikit-engine'
import TUICore, { TUIConstants, ExtensionInfo } from '@tencentcloud/tui-core'
import ChatHeader from './chat-header/index.vue'
import MessageList from './message-list/index.vue'
import MessageInput from './message-input/index.vue'
import MultipleSelectPanel from './mulitple-select-panel/index.vue'
import Forward from './forward/index.vue'
import MessageInputToolbar from './message-input-toolbar/index.vue'
import AudioRecorder from '@/components/AudioRecorder/index.vue'
import SlideUpModal from './slide-up-modal'
import ChatHistoryModal from './chat-history-modal/index.vue'
import { isPC, isWeChat, isUniFrameWork, isMobile, isApp } from '../../utils/env'
import { ToolbarDisplayType } from '../../interface'
import TUIChatConfig from './config'
import useChatStore from '@/store/modules/chat'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { jsbridge } from 'msb-public-library'
import videoPlayImg from '@/assets/images/play.png'
import audioChatImg from '@/assets/images/audio-chat.png'
import { addMessageToQuoteList, saveMediaViaJSBridge } from '@/utils/messageUtils'
import removeIcon from '@/assets/images/close-quit.png'
import { getSessionMessage, deleteHistoryMessages } from '@/api/chatHistory'
import { IMCallbackHandler } from '@/utils/imCallbackHandler'
import { clearIMHistory, sendCustomMessage } from '@/utils/customIM'

const route = useRoute()
const props = defineProps({
  userId: String,
  toAccount: String,
})

const chatStore = useChatStore()
const { toAccount, userId } = props
const emits = defineEmits(['closeChat'])

const groupID = ref(undefined)
const isGroup = ref(false)
const isNotInGroup = ref(false)
const notInGroupReason = ref<number>()
const currentConversationID = ref()
const isMultipleSelectMode = ref(false)
const inputToolbarDisplayType = ref<ToolbarDisplayType>('none')
const messageInputRef = ref()
const messageListRef = ref<InstanceType<typeof MessageList>>()
const chatHeaderRef = ref<InstanceType<typeof ChatHeader>>()
const headerExtensionList = ref<ExtensionInfo[]>([])
const featureConfig = TUIChatConfig.getFeatureConfig()
// 历史对话弹窗状态
const showHistoryModal = ref(false)

onMounted(() => {
  TUIStore.watch(StoreName.CONV, {
    currentConversation: onCurrentConversationUpdate,
  })
})

onUnmounted(() => {
  TUIStore.unwatch(StoreName.CONV, {
    currentConversation: onCurrentConversationUpdate,
  })
  reset()
})

const isInputToolbarShow = computed<boolean>(() => {
  return isUniFrameWork ? inputToolbarDisplayType.value !== 'none' : true
})

const leaveGroupReasonText = computed<string>(() => {
  let text = ''
  switch (notInGroupReason.value) {
    case 4:
      text = TUITranslateService.t('TUIChat.您已被管理员移出群聊')
      break
    case 5:
      text = TUITranslateService.t('TUIChat.该群聊已被解散')
      break
    case 8:
      text = TUITranslateService.t('TUIChat.您已退出该群聊')
      break
    default:
      text = TUITranslateService.t('TUIChat.您已退出该群聊')
      break
  }
  return text
})

const reset = () => {
  TUIConversationService.switchConversation('')
}

const closeChat = (conversationID: string) => {
  emits('closeChat', conversationID)
  reset()
}

// 处理历史对话弹窗
const handleOpenHistoryChat = () => {
  showHistoryModal.value = true
}

const handleCloseHistoryModal = () => {
  showHistoryModal.value = false
}

const handleSelectHistoryItem = (item: any) => {
  console.log('选择历史对话:', item)
  // 进入历史模式，显示历史对话头部
  if (chatHeaderRef.value) {
    chatHeaderRef.value.enterHistoryMode(item.name)
  }

  // TODO: 切换到选中的对话
  // TUIConversationService.switchConversation(item.conversationID)
  chatStore?.setHistoryMessages([])
  chatStore?.setNextReqMessageID('0')
  const nextReqMessageID = chatStore?.nextReqMessageID
  clearIMHistory()
  getSessionMessage({
    sessionId: item.sessionId,
    minId: nextReqMessageID,
  }).then((res: any) => {
    console.log('🚀 ~ handleSelectHistoryItem ~ res:', res.data)
    // 在这里调用IMCallbackHandler.processIMCallbacks
    const messages = IMCallbackHandler.processIMCallbacks(res.data, true)
    console.log("🚀 ~ handleSelectHistoryItem ~ messages:", messages)
    if (res?.data?.length > 0) {
      chatStore?.setNextReqMessageID(res?.data[res?.data?.length - 1]?.id)
    } else {
      chatStore?.setNextReqMessageID('null')
    }
    // console.log('🚀 ~ handleSelectHistoryItem ~ messages:', messages)
    // 把这个消息存到store里

    chatStore.setHistoryMessages(messages)
    // sendCustomMessage({
    //   data: {
    //     businessID: 'ai_say',
    //     content: {
    //       name: 'sayhello',
    //       data: {},
    //     },
    //   },
    // })

  })
}

// 处理退出历史模式
const handleExitHistoryMode = () => {
  console.log('退出历史模式')
  // 可以在这里添加其他退出历史模式的逻辑
  // 比如清除历史对话相关的状态等
}

const insertEmoji = (emojiObj: object) => {
  messageInputRef.value?.insertEmoji(emojiObj)
}

const handleEditor = (message: IMessageModel, type: string) => {
  if (!message || !type) return
  switch (type) {
    case 'reference':
      // todo
      break
    case 'reply':
      // todo
      break
    case 'reedit':
      if (message?.payload?.text) {
        messageInputRef?.value?.reEdit(message?.payload?.text)
      }
      break
    default:
      break
  }
}

const handleGroup = () => {
  headerExtensionList.value[0].listener.onClicked({ groupID: groupID.value })
}

function changeToolbarDisplayType(type: ToolbarDisplayType) {
  inputToolbarDisplayType.value = inputToolbarDisplayType.value === type ? 'none' : type
  if (inputToolbarDisplayType.value !== 'none' && isUniFrameWork) {
    uni.$emit('scroll-to-bottom')
  }
}

function scrollToLatestMessage() {
  messageListRef.value?.scrollToLatestMessage()
}

function toggleMultipleSelectMode(visible?: boolean) {
  isMultipleSelectMode.value = visible === undefined ? !isMultipleSelectMode.value : visible
}

function mergeForwardMessage() {
  messageListRef.value?.mergeForwardMessage()
}

function oneByOneForwardMessage() {
  messageListRef.value?.oneByOneForwardMessage()
}

function updateUIUserNotInGroup(conversation: IConversationModel) {
  if (conversation?.operationType > 0) {
    headerExtensionList.value = []
    isNotInGroup.value = true
    /**
     * 4 - be removed from the group
     * 5 - group is dismissed
     * 8 - quit group
     */
    notInGroupReason.value = conversation?.operationType
  } else {
    isNotInGroup.value = false
    notInGroupReason.value = undefined
  }
}

function onCurrentConversationUpdate(conversation: IConversationModel) {
  updateUIUserNotInGroup(conversation)
  // return when currentConversation is null
  if (!conversation) {
    return
  }
  // return when currentConversationID.value is the same as conversation.conversationID.
  if (currentConversationID.value === conversation?.conversationID) {
    return
  }

  isGroup.value = false
  let conversationType = TUIChatEngine.TYPES.CONV_C2C
  const conversationID = conversation.conversationID
  if (conversationID.startsWith(TUIChatEngine.TYPES.CONV_GROUP)) {
    conversationType = TUIChatEngine.TYPES.CONV_GROUP
    isGroup.value = true
    groupID.value = conversationID.replace(TUIChatEngine.TYPES.CONV_GROUP, '')
  }

  headerExtensionList.value = []
  isMultipleSelectMode.value = false
  // Initialize chatType
  TUIChatConfig.setChatType(conversationType)
  // While converstaion change success, notify callkit and roomkit、or other components.
  TUICore.notifyEvent(TUIConstants.TUIChat.EVENT.CHAT_STATE_CHANGED, TUIConstants.TUIChat.EVENT_SUB_KEY.CHAT_OPENED, { groupID: groupID.value })
  // The TUICustomerServicePlugin plugin determines if the current conversation is a customer service conversation, then sets chatType and activates the conversation.
  TUICore.callService({
    serviceName: TUIConstants.TUICustomerServicePlugin.SERVICE.NAME,
    method: TUIConstants.TUICustomerServicePlugin.SERVICE.METHOD.ACTIVE_CONVERSATION,
    params: { conversationID: conversationID },
  })
  // When open chat in room, close main chat ui and reset theme.
  if (TUIChatConfig.getChatType() === TUIConstants.TUIChat.TYPE.ROOM) {
    if (TUIChatConfig.getFeatureConfig(TUIConstants.TUIChat.FEATURE.InputVoice) === true) {
      TUIChatConfig.setTheme('light')
      currentConversationID.value = ''
      return
    }
  }
  // Get chat header extensions
  if (TUIChatConfig.getChatType() === TUIConstants.TUIChat.TYPE.GROUP) {
    headerExtensionList.value = TUICore.getExtensionList(TUIConstants.TUIChat.EXTENSION.CHAT_HEADER.EXT_ID)
  }
  TUIStore.update(StoreName.CUSTOM, 'activeConversation', conversationID)
  currentConversationID.value = conversationID
}

// const handleAiVideo = () => {
//   chatStore.setTool('假的工具')
// }

const handleAiMore = () => {
  chatStore.setShowAiToolBar(true)
}
const handleTool = (tool: any) => {
  // chatStore.setTool(tool.functionName)
  chatStore.setTool(tool.functionName)
}
const deleteMessage = async () => {
  console.log('onDelete')
  const isStudent = route.query.role === 'student'
  if (isStudent) {
    ElMessage.error('学生无法删除消息')
    return
  }
  const selectedMessages = chatStore.getSelectedMessages()
  if (selectedMessages.length === 0) {
    ElMessage.error('请选择要删除的消息')
    return
  }

  console.log('selectedMessages', selectedMessages)

  // 区分历史记录消息和普通消息
  const historyMessages = selectedMessages.filter(message => (message as any).originalId !== undefined)
  const normalMessages = selectedMessages.filter(message => (message as any).originalId === undefined)

  console.log('historyMessages', historyMessages)
  console.log('normalMessages', normalMessages)

  try {
    const deletePromises: Promise<any>[] = []

    // 处理历史记录消息删除
    if (historyMessages.length > 0) {
      const originalIds = historyMessages.map(message => Number((message as any).originalId))
      console.log('删除历史记录消息的originalIds', originalIds)

      const historyDeletePromise = deleteHistoryMessages(originalIds)
      deletePromises.push(historyDeletePromise)
    }

    // 处理普通消息删除
    if (normalMessages.length > 0) {
      const messageIDs = normalMessages.map(message => message.ID)
      console.log('删除普通消息的messageIDs', messageIDs)

      const normalDeletePromise = Promise.all(
        messageIDs.map(messageID => TUIStore.getMessageModel(messageID)?.deleteMessage())
      )
      deletePromises.push(normalDeletePromise)
    }

    // 并行执行所有删除操作
    await Promise.all(deletePromises)

    // 如果删除了历史消息，需要重新拉取历史消息以更新界面
    if (historyMessages.length > 0) {
      try {
        // 重新拉取历史消息
        const sessionId = chatStore.sessionId
        const minId = '0' // 从头开始拉取，确保获取最新的历史消息状态

        const res: any = await getSessionMessage({
          sessionId: sessionId,
          minId: minId,
        })

        if (res?.data) {
          // 处理消息数据，转换为TUIKit消息格式
          const messages = IMCallbackHandler.processIMCallbacks(res.data, true)
          // 更新历史消息状态
          chatStore.setHistoryMessages(messages)
          // 更新nextReqMessageID用于后续分页加载
          if (res.data.length > 0) {
            chatStore.setNextReqMessageID(res.data[res.data.length - 1]?.id)
          } else {
            chatStore.setNextReqMessageID('null')
          }
          console.log('历史消息删除后重新拉取成功，更新了', messages.length, '条消息')
        }
      } catch (error) {
        console.error('重新拉取历史消息失败:', error)
        // 重新拉取失败不影响删除成功的提示，但需要记录错误
      }
    }

    ElMessage.success('消息删除成功')
  } catch (error) {
    console.error('删除消息失败:', error)
    ElMessage.error('消息删除失败')
  } finally {
    toggleMultipleSelectMode()
  }
}

const saveMessage = () => {
  console.log('onSave')
  const selectedMessages = chatStore.getSelectedMessages()
  console.log('🚀 ~ saveMessage ~ selectedMessages:', selectedMessages)
  if (selectedMessages.length === 0) {
    ElMessage.error('请选择要保存的消息')
    return
  }

  // 判断是否有非视频且非图片的消息
  const hasNonMediaMessage = selectedMessages.some((message: any) => {
    if (message.from.includes('agent')) {
      const customMessage = message?.payload?.data ? JSON.parse(message?.payload?.data) : {}
      console.log('🚀 ~ hasNonMediaMessage ~ customMessage:', customMessage)
      const type = customMessage?.statusMsgData[0].type
      return type !== 'image' && type !== 'video'
    } else {
      const customMessage = message?.payload?.data ? JSON.parse(message?.payload?.data) : {}
      const type = customMessage.content?.name
      return type !== 'image' && type !== 'video'
    }
  })

  if (hasNonMediaMessage) {
    ElMessage.warning('只能保存图片和视频消息')
    toggleMultipleSelectMode()
    return
  }

  // 循环处理选中的消息
  selectedMessages.forEach((message: any) => {
    saveMediaViaJSBridge(message)
  })

  toggleMultipleSelectMode()
}

const quoteMessage = () => {
  console.log('onQuote')
  const selectedMessages = chatStore.getSelectedMessages()
  if (selectedMessages.length === 0) {
    ElMessage.error('请选择要引用的消息')
    return
  }

  // 获取当前引用列表
  const storeQuoteList = chatStore.quoteList
  let newQuoteList = [...storeQuoteList]

  // 处理每条选中的消息
  selectedMessages.forEach((message: any) => {
    // 添加消息到引用列表，合并到现有列表中
    const resultList = addMessageToQuoteList(message)
    newQuoteList = resultList
  })

  toggleMultipleSelectMode()
}
const handleClose = () => {
  chatStore.setSelectTextShow(false)
}
</script>

<style scoped lang="scss" src="./style/index.scss"></style>
<style scoped lang="scss">
.ai-bottom {
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #f1f3fa;

  &.ai-bottom-hidden {
    opacity: 0.3;
    pointer-events: none;
    height: 0;
    overflow: hidden;
  }
}

.multiple-select-overlay {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 101;
  background: #f1f3fa;
  box-shadow: rgba(149, 157, 165, 0.2) 0px -4px 12px;
}

.cite::-webkit-scrollbar {
  display: none;
}

.cite {
  display: flex;
  flex-direction: row;
  overflow-x: auto;
  padding: 20px 16px 0;

  /* 适用于 WebKit 浏览器（如 Chrome、Safari） */
  ::-webkit-scrollbar {
    display: none;
  }

  /* 适用于 Firefox */
  scrollbar-width: none;

  /* 适用于 IE 和 Edge */
  -ms-overflow-style: none;

  // margin-bottom: 20px;
  .cite-item {
    margin: 0 10px;
    flex: 0 0 auto;
    max-width: 230px;
    height: 56px;
    border-radius: 5px;
    background: #dcdcdc;
    overflow: visible;
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    border-radius: 10px;

    .cite-text {
      margin: 0 7px;
      width: 100%;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 13px;
      color: #000000;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: block;
    }

    .cite-image {
      margin: 0 7px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      .cite-title {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #000000;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 10px;
        height: 42px;
      }

      .img {
        width: 42px;
        height: 42px;
        border-radius: 8px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .cite-video {
      margin: 0 7px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      .cite-title {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #000000;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 10px;
        height: 42px;
      }

      .img {
        width: 42px;
        height: 42px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play {
          width: 18px;
          height: 18px;
          position: absolute;
          z-index: 10px;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }

    .cite-audio {
      margin: 0 7px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      .cite-title {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #000000;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 10px;
        height: 42px;
      }

      .img {
        width: 42px;
        height: 42px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .remove {
      position: absolute;
      right: -7px;
      top: -7px;
      width: 14px;
      height: 14px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
      }
    }
  }
}

.ai-toolbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: calc(100% - 50px);
  margin: 0 0 0 25px;
  padding-top: 20px;

  .ai-tool-list {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;

    .ai-tool-item {
      display: flex;
      flex-direction: row;
      padding: 8px;
      background: #fff;
      border-radius: 5px;
      justify-content: center;
      align-items: center;
      margin-right: 15px;

      .ai-tool-item-icon {
        width: 23px;
        height: 23px;
        margin-right: 10px;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .ai-tool-item-text {
        font-weight: normal;
        font-size: 14px;
        color: #000000;
      }
    }
  }

  .ai-more {
    display: flex;
    flex-direction: row;
    width: 80px;
    height: 35px;
    border-radius: 5px;
    justify-content: center;
    align-items: center;
    background-image: url('@/assets/images/more-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;

    .ai-more-icon {
      width: 17px;
      height: 17px;
      background-image: url('@/assets/images/more.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      margin-right: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .ai-more-text {
      font-weight: normal;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
</style>
